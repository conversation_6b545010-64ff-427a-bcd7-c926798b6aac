using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using MoreMountains.InventoryEngine;
using MoreMountains.Tools;

/// <summary>
/// 显示玩家拥有的钥匙数量
/// </summary>
public class KeyCounterDisplay : InventoryCounterDisplay
{
    [Header("Key Settings")]
    [Tooltip("钥匙物品的ID")]
    public string KeyID = "Key";
    
    [Tooltip("是否在开始时自动查找KeyInventory")]
    public bool AutoFindKeyInventory = true;
    
    [Tooltip("玩家ID，用于查找对应的Inventory")]
    public string PlayerID = "Player1";
    
    /// <summary>
    /// 在启用时初始化
    /// </summary>
    protected virtual void Start()
    {
        // 如果Item列表为空，添加Key物品
        if (Item == null || Item.Count == 0)
        {
            Item = new List<InventoryItem>();
            // 尝试从Resources加载Key物品
            InventoryItem keyItem = Resources.Load<InventoryItem>("Items/InventoryKey");
            if (keyItem != null)
            {
                Item.Add(keyItem);
            }
            else
            {
                Debug.LogWarning("KeyCounterDisplay: 无法加载Key物品，请确保Resources/Items/InventoryKey资源存在");
            }
        }
        
        // 如果需要自动查找KeyInventory
        if (AutoFindKeyInventory && (TargetInventories == null || TargetInventories.Count == 0))
        {
            TargetInventories = new List<Inventory>();
            
            // 查找玩家的KeyInventory
            Inventory keyInventory = InventoryManager.Instance.FindInventory(PlayerID, "KeyInventory");
            if (keyInventory != null)
            {
                TargetInventories.Add(keyInventory);
            }
            else
            {
                Debug.LogWarning("KeyCounterDisplay: 无法找到玩家的KeyInventory，请确保场景中存在正确配置的KeyInventory");
            }
        }
        
        // 设置显示格式
        if (string.IsNullOrEmpty(DisplayFormat))
        {
            DisplayFormat = "x{0}";
        }
        
        // 初始化显示
        UpdateCounter();
    }
    
    /// <summary>
    /// 更新钥匙计数器显示
    /// </summary>
    public override void UpdateCounter()
    {
        base.UpdateCounter();
        
        // 如果找不到物品栏或物品，显示为0
        if (TargetInventories == null || TargetInventories.Count == 0 || Item == null || Item.Count == 0)
        {
            if (TargetText != null)
            {
                TargetText.text = string.Format(DisplayFormat, 0);
            }
            return;
        }
    }
    
    /// <summary>
    /// 当物品栏内容变化时更新显示
    /// </summary>
    protected virtual void OnEnable()
    {
        // 注册物品栏变化事件
        MMEventManager.StartListening("InventoryChange", OnInventoryChange);
    }
    
    /// <summary>
    /// 当禁用时取消事件监听
    /// </summary>
    protected virtual void OnDisable()
    {
        // 取消注册物品栏变化事件
        MMEventManager.StopListening("InventoryChange", OnInventoryChange);
    }
    
    /// <summary>
    /// 处理物品栏变化事件
    /// </summary>
    protected virtual void OnInventoryChange(MMEvent inventoryEvent)
    {
        UpdateCounter();
    }
}
