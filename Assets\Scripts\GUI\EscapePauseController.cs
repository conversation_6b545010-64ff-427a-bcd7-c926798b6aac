using UnityEngine;
using System.Collections;
using MoreMountains.Tools;
using MoreMountains.CorgiEngine;
using UnityEngine.SceneManagement;

/// <summary>
/// 通过 ESC 键控制游戏暂停和返回开始界面
/// </summary>
public class EscapePauseController : CorgiMonoBehaviour
{
    /// <summary>
    /// 开始界面的场景名称
    /// </summary>
    [Tooltip("开始界面的场景名称")]
    public string StartScreenSceneName = "StartScreen";
    
    /// <summary>
    /// 加载场景时使用的过渡场景名称
    /// </summary>
    [Tooltip("加载场景时使用的过渡场景名称")]
    public string LoadingSceneName = "LoadingScreen";
    
    /// <summary>
    /// 淡出效果的持续时间
    /// </summary>
    [Tooltip("淡出效果的持续时间")]
    public float FadeOutDuration = 0.3f;
    
    /// <summary>
    /// 淡出效果的缓动类型
    /// </summary>
    [Tooltip("淡出效果的缓动类型")]
    public MMTweenType Tween = new MMTweenType(MMTween.MMTweenCurve.EaseInCubic);
    
    private bool _isPaused = false;
    
    /// <summary>
    /// 每帧检测 ESC 键输入
    /// </summary>
    protected virtual void Update()
    {
        // 检测 ESC 键按下
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            TogglePause();
        }
    }
    
    /// <summary>
    /// 切换游戏暂停状态
    /// </summary>
    public virtual void TogglePause()
    {
        StartCoroutine(TogglePauseCo());
    }
    
    /// <summary>
    /// 切换暂停状态的协程
    /// </summary>
    protected virtual IEnumerator TogglePauseCo()
    {
        yield return null;
        // 触发暂停事件
        CorgiEngineEvent.Trigger(CorgiEngineEventTypes.TogglePause);
        _isPaused = !_isPaused;
    }
    
    /// <summary>
    /// 返回开始界面，可以从暂停菜单中调用
    /// </summary>
    public virtual void ReturnToStartScreen()
    {
        StartCoroutine(ReturnToStartScreenCo());
    }
    
    /// <summary>
    /// 返回开始界面的协程
    /// </summary>
    protected virtual IEnumerator ReturnToStartScreenCo()
    {
        // 如果游戏处于暂停状态，先解除暂停
        if (_isPaused)
        {
            CorgiEngineEvent.Trigger(CorgiEngineEventTypes.UnPause);
            _isPaused = false;
        }
        
        // 触发淡出效果
        MMFadeInEvent.Trigger(FadeOutDuration, Tween, 0, true);
        
        // 等待淡出效果完成
        yield return new WaitForSecondsRealtime(FadeOutDuration);
        
        // 加载开始界面
        MMSceneLoadingManager.LoadScene(StartScreenSceneName, LoadingSceneName);
    }
}